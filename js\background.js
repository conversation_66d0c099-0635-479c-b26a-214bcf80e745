// Background script for ColorNinja extension

chrome.runtime.onInstalled.addListener(() => {
    console.log('ColorNinja extension installed');
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'extractColors') {
        // Inject color extraction script into the active tab
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                function: extractColorsFromPage
            }, (results) => {
                if (results && results[0]) {
                    sendResponse({ colors: results[0].result });
                } else {
                    sendResponse({ colors: [] });
                }
            });
        });
        return true; // Keep the message channel open for async response
    }

    if (request.action === 'startColorPicker') {
        // Inject color picker script into the active tab
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                function: startColorPickerMode
            });
        });
    }

    if (request.action === 'saveColor') {
        // Forward save color request to popup
        chrome.runtime.sendMessage({
            action: 'saveColorFromPicker',
            color: request.color
        });
    }
});

// Function to extract colors from the page
function extractColorsFromPage() {
    const colors = new Map();

    // Helper function to normalize colors to hex
    function normalizeColor(color) {
        if (!color || color === 'transparent' || color === 'none') return null;

        // Create a temporary element to compute color
        const temp = document.createElement('div');
        temp.style.color = color;
        document.body.appendChild(temp);
        const computed = window.getComputedStyle(temp).color;
        document.body.removeChild(temp);

        // Convert RGB to HEX
        const rgb = computed.match(/\d+/g);
        if (rgb) {
            const hex = '#' + rgb.map(x => {
                const hex = parseInt(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            }).join('');
            return hex.toUpperCase();
        }
        return null;
    }

    // Helper function to convert hex to RGB
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Helper function to convert RGB to HSL
    function rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100)
        };
    }

    // Get all elements
    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
        const styles = window.getComputedStyle(element);
        const properties = [
            'color',
            'background-color',
            'border-color',
            'border-top-color',
            'border-right-color',
            'border-bottom-color',
            'border-left-color',
            'outline-color',
            'text-decoration-color',
            'box-shadow',
            'text-shadow'
        ];

        properties.forEach(prop => {
            const value = styles[prop];
            if (value) {
                // Handle multiple colors in properties like box-shadow
                const colorMatches = value.match(/rgb\([^)]+\)|rgba\([^)]+\)|#[a-fA-F0-9]{3,6}|[a-zA-Z]+/g);
                if (colorMatches) {
                    colorMatches.forEach(color => {
                        const normalizedColor = normalizeColor(color);
                        if (normalizedColor) {
                            colors.set(normalizedColor, (colors.get(normalizedColor) || 0) + 1);
                        }
                    });
                }
            }
        });
    });

    // Convert to array and add format information
    const result = Array.from(colors.entries()).map(([hex, count]) => {
        const rgb = hexToRgb(hex);
        const hsl = rgb ? rgbToHsl(rgb.r, rgb.g, rgb.b) : null;

        return {
            hex,
            count,
            rgb: rgb ? `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})` : null,
            hsl: hsl ? `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)` : null
        };
    }).sort((a, b) => b.count - a.count);

    return result;
}

// Function to start color picker mode
function startColorPickerMode() {
    // Remove existing picker if any
    const existingOverlay = document.querySelector('.colorninja-picker-overlay');
    if (existingOverlay) {
        existingOverlay.remove();
    }

    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'colorninja-picker-overlay';

    // Create info box
    const infoBox = document.createElement('div');
    infoBox.className = 'colorninja-picker-info';
    infoBox.textContent = 'Click anywhere to pick a color. Press ESC to cancel.';

    // Create color preview
    const preview = document.createElement('div');
    preview.className = 'colorninja-color-preview';

    // Create magnifier for better precision
    const magnifier = document.createElement('div');
    magnifier.className = 'colorninja-magnifier';
    magnifier.innerHTML = `
        <canvas width="100" height="100" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid white; box-shadow: 0 4px 15px rgba(0,0,0,0.3);"></canvas>
        <div class="magnifier-crosshair"></div>
    `;

    document.body.appendChild(overlay);
    document.body.appendChild(infoBox);
    document.body.appendChild(preview);
    document.body.appendChild(magnifier);

    // Enhanced color picking logic
    function getColorAtPoint(x, y) {
        // Temporarily hide overlay elements
        overlay.style.display = 'none';
        infoBox.style.display = 'none';
        preview.style.display = 'none';
        magnifier.style.display = 'none';

        const element = document.elementFromPoint(x, y);

        // Show overlay elements again
        overlay.style.display = 'block';
        infoBox.style.display = 'block';
        preview.style.display = 'block';
        magnifier.style.display = 'block';

        if (element) {
            return getActualVisibleColor(element, x, y);
        }
        return 'rgb(255, 255, 255)';
    }

    function getActualVisibleColor(element, x, y) {
        const styles = window.getComputedStyle(element);

        // Get element's bounding rect
        const rect = element.getBoundingClientRect();

        // Check if we're clicking on text content
        if (element.textContent && element.textContent.trim()) {
            const textColor = styles.color;
            if (textColor && !isTransparent(textColor)) {
                return textColor;
            }
        }

        // Check for background images or gradients
        const backgroundImage = styles.backgroundImage;
        if (backgroundImage && backgroundImage !== 'none') {
            // For background images, try to get the dominant color or fallback
            const bgColor = styles.backgroundColor;
            if (bgColor && !isTransparent(bgColor)) {
                return bgColor;
            }
        }

        // Check background color
        let color = styles.backgroundColor;
        if (color && !isTransparent(color)) {
            return color;
        }

        // Check if element has a border and we're near the edge
        const borderWidth = parseFloat(styles.borderWidth) || 0;
        if (borderWidth > 0) {
            const isNearBorder = (
                x - rect.left < borderWidth ||
                rect.right - x < borderWidth ||
                y - rect.top < borderWidth ||
                rect.bottom - y < borderWidth
            );

            if (isNearBorder) {
                color = styles.borderColor;
                if (color && !isTransparent(color)) {
                    return color;
                }
            }
        }

        // Traverse up the DOM tree to find background
        let parent = element.parentElement;
        while (parent && parent !== document.documentElement) {
            const parentStyles = window.getComputedStyle(parent);
            color = parentStyles.backgroundColor;
            if (color && !isTransparent(color)) {
                return color;
            }
            parent = parent.parentElement;
        }

        // Check document background
        const docStyles = window.getComputedStyle(document.documentElement);
        color = docStyles.backgroundColor;
        if (color && !isTransparent(color)) {
            return color;
        }

        // Final fallback
        return 'rgb(255, 255, 255)';
    }

    function getVisibleColorAtElement(element) {
        const styles = window.getComputedStyle(element);

        // Try to get the most relevant color based on element type and content
        let color = null;

        // For text elements, prioritize text color
        if (element.textContent && element.textContent.trim()) {
            color = styles.color;
            if (color && !isTransparent(color)) {
                return color;
            }
        }

        // Check background color
        color = styles.backgroundColor;
        if (color && !isTransparent(color)) {
            return color;
        }

        // Check border color if element has visible borders
        const borderWidth = parseFloat(styles.borderWidth) || 0;
        if (borderWidth > 0) {
            color = styles.borderColor;
            if (color && !isTransparent(color)) {
                return color;
            }
        }

        // If still no color found, traverse up the DOM tree to find a background
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            const parentStyles = window.getComputedStyle(parent);
            color = parentStyles.backgroundColor;
            if (color && !isTransparent(color)) {
                return color;
            }
            parent = parent.parentElement;
        }

        // Last resort: check body background
        const bodyStyles = window.getComputedStyle(document.body);
        color = bodyStyles.backgroundColor;
        if (color && !isTransparent(color)) {
            return color;
        }

        // If everything is transparent, return white
        return 'rgba(255, 255, 255, 1)';
    }

    function isTransparent(color) {
        if (!color || color === 'transparent' || color === 'none') {
            return true;
        }

        // Check for rgba with 0 alpha
        const rgbaMatch = color.match(/rgba?\(([^)]+)\)/);
        if (rgbaMatch) {
            const values = rgbaMatch[1].split(',').map(v => parseFloat(v.trim()));
            if (values.length === 4 && values[3] === 0) {
                return true;
            }
        }

        return false;
    }

    function normalizeColor(color) {
        if (!color || isTransparent(color)) {
            return '#FFFFFF';
        }

        // Handle different color formats
        if (color.startsWith('#')) {
            return color.toUpperCase();
        }

        // Convert rgb/rgba to hex
        const rgbaMatch = color.match(/rgba?\(([^)]+)\)/);
        if (rgbaMatch) {
            const values = rgbaMatch[1].split(',').map(v => Math.round(parseFloat(v.trim())));
            const hex = '#' + values.slice(0, 3).map(x => {
                const hex = x.toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            }).join('');
            return hex.toUpperCase();
        }

        // For named colors, use a temporary element to convert
        const temp = document.createElement('div');
        temp.style.color = color;
        document.body.appendChild(temp);
        const computed = window.getComputedStyle(temp).color;
        document.body.removeChild(temp);

        const rgb = computed.match(/\d+/g);
        if (rgb && rgb.length >= 3) {
            const hex = '#' + rgb.slice(0, 3).map(x => {
                const hex = parseInt(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            }).join('');
            return hex.toUpperCase();
        }

        return '#FFFFFF';
    }

    // Mouse move handler
    overlay.addEventListener('mousemove', (e) => {
        const color = getColorAtPoint(e.clientX, e.clientY);
        const hexColor = normalizeColor(color);

        preview.style.backgroundColor = hexColor;
        preview.style.left = (e.clientX + 15) + 'px';
        preview.style.top = (e.clientY - 15) + 'px';

        // Update magnifier position
        magnifier.style.left = (e.clientX + 30) + 'px';
        magnifier.style.top = (e.clientY - 30) + 'px';
    });

    // Click handler
    overlay.addEventListener('click', (e) => {
        const color = getColorAtPoint(e.clientX, e.clientY);
        const hexColor = normalizeColor(color);

        // Copy hex to clipboard automatically
        navigator.clipboard.writeText(hexColor).then(() => {
            // Show enhanced color dialog
            showColorDialog(hexColor, e.clientX, e.clientY);

            // Send message to popup
            chrome.runtime.sendMessage({
                action: 'colorPicked',
                color: hexColor
            });

            // Show brief success message
            showBriefMessage(`${hexColor} copied!`);
        });

        // Clean up
        overlay.remove();
        infoBox.remove();
        preview.remove();
        magnifier.remove();
    });

    // ESC key handler
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            overlay.remove();
            infoBox.remove();
            preview.remove();
            magnifier.remove();
            document.removeEventListener('keydown', escHandler);
        }
    });

    // Enhanced color dialog function
    function showColorDialog(hexColor, x, y) {
        // Remove any existing dialog
        const existingDialog = document.querySelector('.colorninja-color-dialog');
        if (existingDialog) {
            existingDialog.remove();
        }

        // Convert hex to RGB and HSL
        const rgb = hexToRgb(hexColor);
        const hsl = rgb ? rgbToHsl(rgb.r, rgb.g, rgb.b) : null;

        const rgbString = rgb ? `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})` : hexColor;
        const hslString = hsl ? `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)` : hexColor;

        // Create dialog
        const dialog = document.createElement('div');
        dialog.className = 'colorninja-color-dialog';
        dialog.innerHTML = `
            <div class="dialog-header">
                <div class="color-preview-large" style="background-color: ${hexColor}"></div>
                <button class="dialog-close">×</button>
            </div>
            <div class="dialog-content">
                <div class="color-info">
                    <h3>Picked Color</h3>
                    <div class="color-value">${hexColor}</div>
                </div>
                <div class="format-buttons">
                    <button class="format-copy-btn" data-value="${hexColor}" data-format="HEX">
                        <span class="format-label">HEX</span>
                        <span class="format-value">${hexColor}</span>
                    </button>
                    <button class="format-copy-btn" data-value="${rgbString}" data-format="RGB">
                        <span class="format-label">RGB</span>
                        <span class="format-value">${rgbString}</span>
                    </button>
                    <button class="format-copy-btn" data-value="${hslString}" data-format="HSL">
                        <span class="format-label">HSL</span>
                        <span class="format-value">${hslString}</span>
                    </button>
                </div>
                <button class="save-color-btn">Save to Collection</button>
            </div>
        `;

        // Position dialog near click point but keep it on screen
        const dialogWidth = 320;
        const dialogHeight = 280;
        const margin = 20;

        let dialogX = x + 20;
        let dialogY = y - dialogHeight / 2;

        // Keep dialog on screen
        if (dialogX + dialogWidth > window.innerWidth - margin) {
            dialogX = x - dialogWidth - 20;
        }
        if (dialogY < margin) {
            dialogY = margin;
        }
        if (dialogY + dialogHeight > window.innerHeight - margin) {
            dialogY = window.innerHeight - dialogHeight - margin;
        }

        dialog.style.left = dialogX + 'px';
        dialog.style.top = dialogY + 'px';

        document.body.appendChild(dialog);

        // Add event listeners
        const closeBtn = dialog.querySelector('.dialog-close');
        closeBtn.addEventListener('click', () => {
            dialog.remove();
        });

        const formatBtns = dialog.querySelectorAll('.format-copy-btn');
        formatBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const value = btn.getAttribute('data-value');
                const format = btn.getAttribute('data-format');
                navigator.clipboard.writeText(value).then(() => {
                    showBriefMessage(`${format} copied!`);
                });
            });
        });

        const saveBtn = dialog.querySelector('.save-color-btn');
        saveBtn.addEventListener('click', () => {
            // Send message to save color
            chrome.runtime.sendMessage({
                action: 'saveColor',
                color: {
                    hex: hexColor,
                    rgb: rgbString,
                    hsl: hslString
                }
            });
            showBriefMessage('Color saved!');
            dialog.remove();
        });

        // Auto-close after 10 seconds
        setTimeout(() => {
            if (document.body.contains(dialog)) {
                dialog.remove();
            }
        }, 10000);
    }

    function showBriefMessage(message) {
        const msg = document.createElement('div');
        msg.className = 'colorninja-brief-message';
        msg.textContent = message;
        msg.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-family: 'Segoe UI', sans-serif;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000003;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            animation: slideInOut 3s ease-out forwards;
        `;

        // Add animation styles if not already present
        if (!document.querySelector('#colorninja-brief-animation')) {
            const style = document.createElement('style');
            style.id = 'colorninja-brief-animation';
            style.textContent = `
                @keyframes slideInOut {
                    0% { opacity: 0; transform: translateX(100%); }
                    15% { opacity: 1; transform: translateX(0); }
                    85% { opacity: 1; transform: translateX(0); }
                    100% { opacity: 0; transform: translateX(100%); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(msg);

        setTimeout(() => {
            if (document.body.contains(msg)) {
                msg.remove();
            }
        }, 3000);
    }
}