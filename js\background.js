// Background script for ColorNinja extension

chrome.runtime.onInstalled.addListener(() => {
    console.log('ColorNinja extension installed');
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'extractColors') {
        // Inject color extraction script into the active tab
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                function: extractColorsFromPage
            }, (results) => {
                if (results && results[0]) {
                    sendResponse({ colors: results[0].result });
                } else {
                    sendResponse({ colors: [] });
                }
            });
        });
        return true; // Keep the message channel open for async response
    }

    if (request.action === 'startColorPicker') {
        // Inject color picker script into the active tab
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                function: startColorPickerMode
            });
        });
    }
});

// Function to extract colors from the page
function extractColorsFromPage() {
    const colors = new Map();

    // Helper function to normalize colors to hex
    function normalizeColor(color) {
        if (!color || color === 'transparent' || color === 'none') return null;

        // Create a temporary element to compute color
        const temp = document.createElement('div');
        temp.style.color = color;
        document.body.appendChild(temp);
        const computed = window.getComputedStyle(temp).color;
        document.body.removeChild(temp);

        // Convert RGB to HEX
        const rgb = computed.match(/\d+/g);
        if (rgb) {
            const hex = '#' + rgb.map(x => {
                const hex = parseInt(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            }).join('');
            return hex.toUpperCase();
        }
        return null;
    }

    // Helper function to convert hex to RGB
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Helper function to convert RGB to HSL
    function rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100)
        };
    }

    // Get all elements
    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
        const styles = window.getComputedStyle(element);
        const properties = [
            'color',
            'background-color',
            'border-color',
            'border-top-color',
            'border-right-color',
            'border-bottom-color',
            'border-left-color',
            'outline-color',
            'text-decoration-color',
            'box-shadow',
            'text-shadow'
        ];

        properties.forEach(prop => {
            const value = styles[prop];
            if (value) {
                // Handle multiple colors in properties like box-shadow
                const colorMatches = value.match(/rgb\([^)]+\)|rgba\([^)]+\)|#[a-fA-F0-9]{3,6}|[a-zA-Z]+/g);
                if (colorMatches) {
                    colorMatches.forEach(color => {
                        const normalizedColor = normalizeColor(color);
                        if (normalizedColor) {
                            colors.set(normalizedColor, (colors.get(normalizedColor) || 0) + 1);
                        }
                    });
                }
            }
        });
    });

    // Convert to array and add format information
    const result = Array.from(colors.entries()).map(([hex, count]) => {
        const rgb = hexToRgb(hex);
        const hsl = rgb ? rgbToHsl(rgb.r, rgb.g, rgb.b) : null;

        return {
            hex,
            count,
            rgb: rgb ? `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})` : null,
            hsl: hsl ? `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)` : null
        };
    }).sort((a, b) => b.count - a.count);

    return result;
}

// Function to start color picker mode
function startColorPickerMode() {
    // Remove existing picker if any
    const existingOverlay = document.querySelector('.colorninja-picker-overlay');
    if (existingOverlay) {
        existingOverlay.remove();
    }

    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'colorninja-picker-overlay';

    // Create info box
    const infoBox = document.createElement('div');
    infoBox.className = 'colorninja-picker-info';
    infoBox.textContent = 'Click anywhere to pick a color. Press ESC to cancel.';

    // Create color preview
    const preview = document.createElement('div');
    preview.className = 'colorninja-color-preview';

    document.body.appendChild(overlay);
    document.body.appendChild(infoBox);
    document.body.appendChild(preview);

    // Color picking logic
    function getColorAtPoint(x, y) {
        // Temporarily hide overlay elements
        overlay.style.display = 'none';
        infoBox.style.display = 'none';
        preview.style.display = 'none';

        const element = document.elementFromPoint(x, y);

        // Show overlay elements again
        overlay.style.display = 'block';
        infoBox.style.display = 'block';
        preview.style.display = 'block';

        if (element) {
            const styles = window.getComputedStyle(element);
            return styles.backgroundColor;
        }
        return 'transparent';
    }

    function normalizeColor(color) {
        if (!color || color === 'transparent' || color === 'rgba(0, 0, 0, 0)') {
            return '#FFFFFF';
        }

        const temp = document.createElement('div');
        temp.style.color = color;
        document.body.appendChild(temp);
        const computed = window.getComputedStyle(temp).color;
        document.body.removeChild(temp);

        const rgb = computed.match(/\d+/g);
        if (rgb) {
            const hex = '#' + rgb.map(x => {
                const hex = parseInt(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            }).join('');
            return hex.toUpperCase();
        }
        return '#FFFFFF';
    }

    // Mouse move handler
    overlay.addEventListener('mousemove', (e) => {
        const color = getColorAtPoint(e.clientX, e.clientY);
        const hexColor = normalizeColor(color);

        preview.style.backgroundColor = hexColor;
        preview.style.left = e.clientX + 'px';
        preview.style.top = e.clientY + 'px';
    });

    // Click handler
    overlay.addEventListener('click', (e) => {
        const color = getColorAtPoint(e.clientX, e.clientY);
        const hexColor = normalizeColor(color);

        // Copy to clipboard
        navigator.clipboard.writeText(hexColor).then(() => {
            // Send message to popup
            chrome.runtime.sendMessage({
                action: 'colorPicked',
                color: hexColor
            });

            // Show success message
            const successMsg = document.createElement('div');
            successMsg.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #48bb78;
                color: white;
                padding: 16px 24px;
                border-radius: 8px;
                font-family: 'Segoe UI', sans-serif;
                font-size: 16px;
                font-weight: 500;
                z-index: 1000002;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
                animation: fadeInOut 2s ease-out forwards;
            `;
            successMsg.textContent = `Color ${hexColor} copied to clipboard!`;

            // Add animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInOut {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(successMsg);

            setTimeout(() => {
                successMsg.remove();
                style.remove();
            }, 2000);
        });

        // Clean up
        overlay.remove();
        infoBox.remove();
        preview.remove();
    });

    // ESC key handler
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            overlay.remove();
            infoBox.remove();
            preview.remove();
            document.removeEventListener('keydown', escHandler);
        }
    });
}