/* CSS Variables for Theming */
:root {
    --bg-primary: #ffffff;
    --bg-secondary: rgba(255, 255, 255, 0.8);
    --bg-glass: rgba(255, 255, 255, 0.1);
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    --accent-primary: #667eea;
    --accent-secondary: #764ba2;
    --accent-tertiary: #f093fb;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-medium: 0 4px 15px rgba(0, 0, 0, 0.1);
    --backdrop-blur: blur(10px);
    --success-color: #48bb78;
    --danger-color: #f56565;
    --warning-color: #ed8936;
}

[data-theme="dark"] {
    --bg-primary: #1a202c;
    --bg-secondary: rgba(26, 32, 44, 0.8);
    --bg-glass: rgba(255, 255, 255, 0.05);
    --text-primary: #f7fafc;
    --text-secondary: #cbd5e0;
    --text-muted: #718096;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.5);
    --shadow-medium: 0 4px 15px rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 50%, var(--accent-tertiary) 100%);
    min-height: 100vh;
    transition: all 0.3s ease;
}

.container {
    width: 400px;
    min-height: 600px;
    background: var(--bg-glass);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    position: relative;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--bg-glass);
    backdrop-filter: var(--backdrop-blur);
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-icon {
    font-size: 24px;
    animation: bounce 2s infinite;
}

.logo h1 {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.theme-toggle {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: var(--backdrop-blur);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
}

.theme-icon {
    font-size: 18px;
    display: block;
}

/* Actions Section */
.actions {
    padding: 20px;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn {
    flex: 1;
    min-width: 110px;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--bg-glass);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    backdrop-filter: var(--backdrop-blur);
}

.btn-accent {
    background: linear-gradient(135deg, var(--accent-tertiary), var(--accent-primary));
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-icon {
    font-size: 16px;
}

/* Section Styles */
.section {
    margin: 20px;
    background: var(--bg-glass);
    backdrop-filter: var(--backdrop-blur);
    border-radius: 16px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.section-header {
    padding: 16px 20px;
    background: var(--bg-glass);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h3 {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.color-count {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    background: var(--bg-glass);
    padding: 4px 8px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Colors Grid */
.colors-grid {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.colors-grid::-webkit-scrollbar {
    width: 6px;
}

.colors-grid::-webkit-scrollbar-track {
    background: var(--bg-glass);
    border-radius: 3px;
}

.colors-grid::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

.color-item {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 12px;
    backdrop-filter: var(--backdrop-blur);
    transition: all 0.3s ease;
    position: relative;
}

.color-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.color-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.color-swatch::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #ccc 75%), 
                linear-gradient(-45deg, transparent 75%, #ccc 75%);
    background-size: 8px 8px;
    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
    z-index: -1;
}

.color-swatch:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.color-info {
    flex: 1;
}

.color-hex {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 4px;
}

.color-count-text {
    color: var(--text-secondary);
    font-size: 12px;
}

.color-formats {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 8px;
}

.format-btn {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 11px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.format-btn:hover {
    background: var(--accent-primary);
    color: white;
    transform: translateY(-1px);
}

.save-btn, .remove-btn {
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.remove-btn {
    background: var(--danger-color);
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.save-btn:hover, .remove-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

/* Color Picker Section */
.picker-instruction {
    color: var(--text-secondary);
    text-align: center;
    padding: 20px;
    font-style: italic;
}

.picked-color-container {
    padding: 16px;
}

.picked-color-item {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: var(--backdrop-blur);
    margin-bottom: 12px;
}

/* Footer */
.footer {
    padding: 16px 20px;
    text-align: center;
    background: var(--bg-glass);
    border-top: 1px solid var(--border-color);
    backdrop-filter: var(--backdrop-blur);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.footer p {
    color: var(--text-secondary);
    font-size: 12px;
    margin: 0;
}

.heart {
    color: #e53e3e;
    animation: heartbeat 1.5s ease-in-out infinite;
    display: inline-block;
}

.linkedin-link {
    color: var(--text-secondary);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.linkedin-link:hover {
    color: #0077b5;
    transform: scale(1.1);
}

/* Toast Notification */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--success-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.loading {
    pointer-events: none;
    opacity: 0.7;
}

/* Animations */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes heartbeat {
    0%, 50%, 100% {
        transform: scale(1);
    }
    25%, 75% {
        transform: scale(1.1);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 420px) {
    .container {
        width: 100%;
        min-height: 100vh;
        border-radius: 0;
    }

    .actions {
        flex-direction: column;
    }

    .btn {
        min-width: auto;
    }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 14px;
    line-height: 1.5;
}