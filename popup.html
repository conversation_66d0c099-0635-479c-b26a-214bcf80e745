<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorNinja</title>
    <link rel="stylesheet" href="css/popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="logo-icon">🎨</span>
                    <h1>ColorNinja</h1>
                </div>
            </div>
            <div class="header-right">
                <button id="themeToggle" class="theme-toggle" title="Toggle Theme">
                    <span class="theme-icon">🌙</span>
                </button>
            </div>
        </div>

        <!-- Main Actions -->
        <div class="actions">
            <button id="extractColors" class="btn btn-primary">
                <span class="btn-icon">🔍</span>
                Extract Colors
            </button>
            <button id="colorPicker" class="btn btn-secondary">
                <span class="btn-icon">🎯</span>
                Color Picker
            </button>
            <button id="downloadJson" class="btn btn-accent">
                <span class="btn-icon">📥</span>
                Download JSON
            </button>
        </div>

        <!-- Color Picker Section -->
        <div id="pickerSection" class="section hidden">
            <div class="section-header">
                <h3>Color Picker</h3>
            </div>
            <div id="pickedColorContainer" class="picked-color-container">
                <p class="picker-instruction">Click "Color Picker" and then click anywhere on the webpage to pick a color</p>
            </div>
        </div>

        <!-- Extracted Colors Section -->
        <div id="extractedSection" class="section hidden">
            <div class="section-header">
                <h3>Extracted Colors</h3>
                <span id="colorCount" class="color-count">0 colors found</span>
            </div>
            <div id="extractedColors" class="colors-grid">
                <!-- Extracted colors will be populated here -->
            </div>
        </div>

        <!-- Saved Colors Section -->
        <div id="savedSection" class="section">
            <div class="section-header">
                <h3>Saved Colours</h3>
                <span id="savedCount" class="color-count">0 saved</span>
            </div>
            <div id="savedColors" class="colors-grid">
                <!-- Saved colors will be populated here -->
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Made with <span class="heart">❤️</span> by Deepak Raj</p>
            <a href="https://www.linkedin.com/in/deepak-rajj/" target="_blank" class="linkedin-link" title="Connect on LinkedIn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
            </a>
        </div>

        <!-- Toast Notification -->
        <div id="toast" class="toast hidden">
            <span id="toastMessage"></span>
        </div>
    </div>

    <script src="js/popup.js"></script>
</body>
</html>