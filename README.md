# ColorNinja Chrome Extension

A powerful Chrome extension for extracting, interacting with, and managing colors from any webpage.

## Features

### 🎨 Color Extraction
- Extract all visible colors from any webpage
- Analyze inline styles, internal and external stylesheets
- Count color occurrences and sort by usage
- Support for backgrounds, borders, fonts, and more

### 🎯 Color Picker
- Interactive eye-dropper tool for picking colors from any element
- Real-time color preview while hovering
- Automatic clipboard copying with confirmation

### 💾 Color Management
- Save colors for future reference
- Persistent storage across browser sessions
- Tab-specific color extraction data
- Remove saved colors with one click

### 🔄 Multiple Format Support
- Copy colors in HEX, RGB, and HSL formats
- Click format buttons for instant clipboard copying
- Hover tooltips showing color codes

### 🌙 Theme Support
- Toggle between light and dark themes
- Modern glassmorphism UI design
- Smooth animations and transitions

### 📥 Export Functionality
- Download color data as JSON files
- Include extraction metadata and timestamps
- Organized file naming with page titles

## Installation

1. Download the ColorNinja.zip file
2. Extract the contents to a folder
3. Open Chrome and go to `chrome://extensions/`
4. Enable "Developer mode" in the top right
5. Click "Load unpacked" and select the extracted ColorNinja folder
6. The extension is now ready to use!

## Usage

1. **Extract Colors**: Click the "Extract Colors" button to analyze the current webpage
2. **Pick Colors**: Use the "Color Picker" tool to select specific colors from elements
3. **Save Colors**: Click "Save Color" on any extracted or picked color
4. **Copy Formats**: Click HEX, RGB, or HSL buttons to copy in different formats
5. **Download Data**: Export all color data as JSON for external use
6. **Toggle Theme**: Switch between light and dark modes

## Permissions

- `activeTab`: Access current tab content for color extraction
- `storage`: Save user preferences and color data
- `scripting`: Inject color extraction and picker scripts

## Technical Details

- **Manifest Version**: 3
- **Storage**: Uses both localStorage and chrome.storage.session
- **Tab Persistence**: Colors persist while tab remains open
- **Format Support**: HEX, RGB, HSL color formats
- **UI Framework**: Vanilla JavaScript with modern CSS

## Privacy

- No data is sent to external servers
- All processing happens locally in your browser
- Saved colors are stored only on your device

## Credits

Made with ❤️ by Deepak Raj
Connect on [LinkedIn](https://www.linkedin.com/in/deepak-rajj/)

## Version History

### v1.0.0
- Initial release
- Color extraction functionality
- Eye-dropper color picker
- Save/remove color management
- Multi-format support (HEX, RGB, HSL)
- Light/dark theme toggle
- JSON export functionality
- Glassmorphism UI design
