// Popup script for ColorNinja extension

class ColorNinja {
    constructor() {
        this.extractedColors = [];
        this.savedColors = this.loadSavedColors();
        this.currentTheme = localStorage.getItem('colorNinja_theme') || 'light';
        this.tabId = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.applyTheme();
        this.renderSavedColors();
        this.getCurrentTabId();
        this.loadTabColors();

        // Listen for color picker messages
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'colorPicked') {
                this.handleColorPicked(request.color);
            }
            if (request.action === 'saveColorFromPicker') {
                this.saveColorFromPicker(request.color);
            }
        });
    }

    getCurrentTabId() {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            this.tabId = tabs[0].id;
        });
    }

    setupEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Extract colors button
        document.getElementById('extractColors').addEventListener('click', () => {
            this.extractColors();
        });

        // Color picker button
        document.getElementById('colorPicker').addEventListener('click', () => {
            this.startColorPicker();
        });

        // Download JSON button
        document.getElementById('downloadJson').addEventListener('click', () => {
            this.downloadJson();
        });
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('colorNinja_theme', this.currentTheme);
    }

    applyTheme() {
        document.body.setAttribute('data-theme', this.currentTheme);
        const themeIcon = document.querySelector('.theme-icon');
        themeIcon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
    }

    extractColors() {
        const button = document.getElementById('extractColors');
        button.classList.add('loading');
        button.innerHTML = '<span class="btn-icon">⏳</span>Extracting...';

        chrome.runtime.sendMessage({ action: 'extractColors' }, (response) => {
            button.classList.remove('loading');
            button.innerHTML = '<span class="btn-icon">🔍</span>Extract Colors';

            if (response && response.colors) {
                this.extractedColors = response.colors;
                this.saveTabColors();
                this.renderExtractedColors();
                this.showSection('extractedSection');
            } else {
                this.showToast('No colors found on this page', 'warning');
            }
        });
    }

    startColorPicker() {
        // Close popup to allow color picking
        chrome.runtime.sendMessage({ action: 'startColorPicker' });
        window.close();
    }

    handleColorPicked(hexColor) {
        // Add picked color to picker section
        this.showPickedColor(hexColor);
        this.showSection('pickerSection');
        this.showToast(`Color ${hexColor} copied to clipboard!`);
    }

    showPickedColor(hexColor) {
        const container = document.getElementById('pickedColorContainer');

        // Clear existing content
        container.innerHTML = '';

        // Create picked color item
        const colorItem = this.createColorItem({
            hex: hexColor,
            count: 1,
            rgb: this.hexToRgb(hexColor),
            hsl: this.hexToHsl(hexColor)
        }, true);

        container.appendChild(colorItem);
    }

    saveColorFromPicker(colorData) {
        // Create color object in the expected format
        const color = {
            hex: colorData.hex,
            count: 1,
            rgb: colorData.rgb,
            hsl: colorData.hsl
        };

        this.saveColor(color);
    }

    renderExtractedColors() {
        const container = document.getElementById('extractedColors');
        const countElement = document.getElementById('colorCount');

        container.innerHTML = '';
        countElement.textContent = `${this.extractedColors.length} colors found`;

        if (this.extractedColors.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🎨</div>
                    <div class="empty-state-text">No colors extracted yet.<br>Click "Extract Colors" to get started.</div>
                </div>
            `;
            return;
        }

        this.extractedColors.forEach(color => {
            const colorItem = this.createColorItem(color, false);
            container.appendChild(colorItem);
        });
    }

    renderSavedColors() {
        const container = document.getElementById('savedColors');
        const countElement = document.getElementById('savedCount');

        container.innerHTML = '';
        countElement.textContent = `${this.savedColors.length} saved`;

        if (this.savedColors.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">💾</div>
                    <div class="empty-state-text">No saved colours yet.<br>Save colors from extracted or picked colors.</div>
                </div>
            `;
            return;
        }

        this.savedColors.forEach(color => {
            const colorItem = this.createColorItem(color, false, true);
            container.appendChild(colorItem);
        });
    }

    createColorItem(color, isPicked = false, isSaved = false) {
        const item = document.createElement('div');
        item.className = 'color-item slide-in';

        const rgb = color.rgb || this.hexToRgb(color.hex);
        const hsl = color.hsl || this.hexToHsl(color.hex);

        item.innerHTML = `
            <div class="color-header">
                <div class="color-swatch" style="background-color: ${color.hex}" title="${color.hex}"></div>
                <div class="color-info">
                    <div class="color-hex">${color.hex}</div>
                    ${!isPicked ? `<div class="color-count-text">${color.count} occurrence${color.count !== 1 ? 's' : ''}</div>` : ''}
                </div>
                ${isSaved ? `<button class="remove-btn" title="Remove from saved">×</button>` : ''}
            </div>
            <div class="color-formats">
                <button class="format-btn" data-format="hex" data-value="${color.hex}">HEX</button>
                <button class="format-btn" data-format="rgb" data-value="${rgb}">RGB</button>
                <button class="format-btn" data-format="hsl" data-value="${hsl}">HSL</button>
            </div>
            ${!isSaved ? `<button class="save-btn">Save Color</button>` : ''}
        `;

        // Add event listeners
        const swatch = item.querySelector('.color-swatch');
        swatch.addEventListener('click', () => {
            this.copyToClipboard(color.hex);
            this.showToast('Copied!');
        });

        const formatBtns = item.querySelectorAll('.format-btn');
        formatBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const value = btn.getAttribute('data-value');
                this.copyToClipboard(value);
                this.showToast('Copied!');
            });
        });

        const saveBtn = item.querySelector('.save-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveColor(color);
            });
        }

        const removeBtn = item.querySelector('.remove-btn');
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                this.removeSavedColor(color.hex);
            });
        }

        return item;
    }

    saveColor(color) {
        const existingIndex = this.savedColors.findIndex(saved => saved.hex === color.hex);
        if (existingIndex === -1) {
            this.savedColors.push({
                hex: color.hex,
                rgb: color.rgb || this.hexToRgb(color.hex),
                hsl: color.hsl || this.hexToHsl(color.hex),
                savedAt: new Date().toISOString()
            });
            this.saveSavedColors();
            this.renderSavedColors();
            this.showToast('Color saved!');
        } else {
            this.showToast('Color already saved!', 'warning');
        }
    }

    removeSavedColor(hex) {
        this.savedColors = this.savedColors.filter(color => color.hex !== hex);
        this.saveSavedColors();
        this.renderSavedColors();
        this.showToast('Color removed!');
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).catch(() => {
            // Fallback for older browsers
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        });
    }

    showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');

        toastMessage.textContent = message;
        toast.className = `toast show ${type}`;

        setTimeout(() => {
            toast.className = 'toast hidden';
        }, 2000);
    }

    showSection(sectionId) {
        const section = document.getElementById(sectionId);
        section.classList.remove('hidden');
    }

    downloadJson() {
        if (this.extractedColors.length === 0) {
            this.showToast('No extracted colors to download. Extract colors first!', 'warning');
            return;
        }

        const data = {
            url: window.location.href,
            extractedAt: new Date().toISOString(),
            totalColors: this.extractedColors.length,
            colors: this.extractedColors,
            savedColors: this.savedColors
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            const filename = `colorninja-${this.sanitizeFilename(tabs[0].title)}-${new Date().getTime()}.json`;

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showToast('Colors downloaded!');
        });
    }

    sanitizeFilename(filename) {
        return filename.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    }

    loadSavedColors() {
        const saved = localStorage.getItem('colorNinja_savedColors');
        return saved ? JSON.parse(saved) : [];
    }

    saveSavedColors() {
        localStorage.setItem('colorNinja_savedColors', JSON.stringify(this.savedColors));
    }

    saveTabColors() {
        if (this.tabId) {
            const key = `colorNinja_tab_${this.tabId}`;
            chrome.storage.session.set({ [key]: this.extractedColors });
        }
    }

    loadTabColors() {
        if (this.tabId) {
            const key = `colorNinja_tab_${this.tabId}`;
            chrome.storage.session.get([key], (result) => {
                if (result[key]) {
                    this.extractedColors = result[key];
                    this.renderExtractedColors();
                    this.showSection('extractedSection');
                }
            });
        }
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (result) {
            const r = parseInt(result[1], 16);
            const g = parseInt(result[2], 16);
            const b = parseInt(result[3], 16);
            return `rgb(${r}, ${g}, ${b})`;
        }
        return null;
    }

    hexToHsl(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (!result) return null;

        let r = parseInt(result[1], 16) / 255;
        let g = parseInt(result[2], 16) / 255;
        let b = parseInt(result[3], 16) / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return `hsl(${Math.round(h * 360)}, ${Math.round(s * 100)}%, ${Math.round(l * 100)}%)`;
    }
}

// Initialize the extension when popup loads
document.addEventListener('DOMContentLoaded', () => {
    new ColorNinja();
});