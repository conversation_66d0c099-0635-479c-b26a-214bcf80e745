// Content script for ColorNinja extension

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'colorPicked') {
        // Color was picked, this will be handled by the popup
        console.log('Color picked:', request.color);
    }
});

// Initialize content script
console.log('ColorNinja content script loaded');