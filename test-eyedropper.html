<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EyeDropper API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            max-width: 600px;
            margin: 0 auto;
        }
        .color-box {
            width: 200px;
            height: 100px;
            margin: 20px;
            border-radius: 8px;
            display: inline-block;
            color: white;
            text-align: center;
            line-height: 100px;
            font-weight: bold;
        }
        .red { background-color: #e74c3c; }
        .blue { background-color: #3498db; }
        .green { background-color: #2ecc71; }
        .yellow { background-color: #f1c40f; color: #333; }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 EyeDropper API Test</h1>
        
        <div class="result" id="apiStatus">
            <strong>EyeDropper API Status:</strong> <span id="status">Checking...</span>
        </div>
        
        <button class="test-btn" onclick="testEyeDropper()">Test Native EyeDropper</button>
        
        <h2>Test Colors</h2>
        <div class="color-box red">Red #e74c3c</div>
        <div class="color-box blue">Blue #3498db</div>
        <div class="color-box green">Green #2ecc71</div>
        <div class="color-box yellow">Yellow #f1c40f</div>
        
        <div class="result" id="result" style="display: none;">
            <strong>Picked Color:</strong> <span id="pickedColor"></span>
            <div style="width: 50px; height: 50px; border-radius: 8px; margin-top: 10px;" id="colorPreview"></div>
        </div>
    </div>

    <script>
        // Check EyeDropper API availability
        function checkEyeDropperSupport() {
            const statusEl = document.getElementById('status');
            if ('EyeDropper' in window) {
                statusEl.textContent = '✅ Supported - Modern color picking available!';
                statusEl.style.color = '#28a745';
            } else {
                statusEl.textContent = '❌ Not supported - Will use fallback method';
                statusEl.style.color = '#dc3545';
            }
        }

        // Test native EyeDropper
        async function testEyeDropper() {
            if (!('EyeDropper' in window)) {
                alert('EyeDropper API not supported in this browser');
                return;
            }

            try {
                const eyeDropper = new EyeDropper();
                const result = await eyeDropper.open();
                
                document.getElementById('result').style.display = 'block';
                document.getElementById('pickedColor').textContent = result.sRGBHex;
                document.getElementById('colorPreview').style.backgroundColor = result.sRGBHex;
                
                // Copy to clipboard
                navigator.clipboard.writeText(result.sRGBHex);
                alert(`Color ${result.sRGBHex} copied to clipboard!`);
            } catch (err) {
                console.log('Color picking cancelled:', err);
            }
        }

        // Check support on page load
        checkEyeDropperSupport();
    </script>
</body>
</html>
