/* Color Picker Overlay Styles */
.colorninja-picker-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: transparent;
    z-index: 999999;
    cursor: crosshair;
}

.colorninja-picker-info {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000000;
    pointer-events: none;
    animation: fadeIn 0.3s ease-out;
}

.colorninja-color-preview {
    position: fixed;
    pointer-events: none;
    width: 40px;
    height: 40px;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000001;
    transform: translate(-50%, -50%);
}

/* Magnifier styles */
.colorninja-magnifier {
    position: fixed;
    pointer-events: none;
    z-index: 1000001;
    transform: translate(-50%, -50%);
}

.magnifier-crosshair {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 2px;
    background: white;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 0 1px black;
}

/* Enhanced Color Dialog */
.colorninja-color-dialog {
    position: fixed;
    width: 320px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 1000002;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    animation: dialogSlideIn 0.3s ease-out;
}

.dialog-header {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.color-preview-large {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
    margin-right: 15px;
}

.dialog-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.dialog-close:hover {
    background: #e9ecef;
    color: #495057;
}

.dialog-content {
    padding: 20px;
}

.color-info h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.color-value {
    font-size: 16px;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 20px;
}

.format-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.format-copy-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
}

.format-copy-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.format-label {
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.format-value {
    font-family: 'Consolas', 'Monaco', monospace;
    color: #6c757d;
    font-size: 14px;
}

.save-color-btn {
    width: 100%;
    padding: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-family: inherit;
}

.save-color-btn:hover {
    background: #0056b3;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}