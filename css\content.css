/* Color Picker Overlay Styles */
.colorninja-picker-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.1);
    z-index: 999999;
    cursor: crosshair;
    backdrop-filter: blur(2px);
}

.colorninja-picker-info {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000000;
    pointer-events: none;
    animation: fadeIn 0.3s ease-out;
}

.colorninja-color-preview {
    position: fixed;
    pointer-events: none;
    width: 40px;
    height: 40px;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000001;
    transform: translate(-50%, -50%);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}