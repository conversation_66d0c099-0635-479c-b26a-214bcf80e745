{"manifest_version": 3, "name": "ColorNinja", "version": "1.0.0", "description": "Extract, interact with, and manage colors from any webpage with advanced tools and beautiful UI", "permissions": ["activeTab", "storage", "scripting"], "host_permissions": ["<all_urls>"], "action": {"default_popup": "popup.html", "default_title": "ColorNinja - Color Extractor", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["js/content.js"], "css": ["css/content.css"]}], "background": {"service_worker": "js/background.js"}}