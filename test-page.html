<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorNinja Test Page</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .color-boxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .color-box {
            height: 100px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .color-box:hover {
            transform: scale(1.05);
        }
        
        .red { background-color: #e74c3c; }
        .blue { background-color: #3498db; }
        .green { background-color: #2ecc71; }
        .yellow { background-color: #f1c40f; color: #333; text-shadow: none; }
        .purple { background-color: #9b59b6; }
        .orange { background-color: #e67e22; }
        
        .text-samples {
            margin-top: 30px;
        }
        
        .text-red { color: #e74c3c; }
        .text-blue { color: #3498db; }
        .text-green { color: #2ecc71; }
        
        .border-sample {
            border: 4px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            border-left: 4px solid #007bff;
        }
        
        .dark-section {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .dark-section h3 {
            color: #ecf0f1;
        }
        
        .gradient-text {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 24px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ColorNinja Test Page</h1>
        
        <div class="instructions">
            <h3>🎯 Test Instructions</h3>
            <p>1. Click the ColorNinja extension icon</p>
            <p>2. Click "Color Picker" button</p>
            <p>3. Try picking colors from different elements below</p>
            <p>4. The color picker should now show accurate colors and display an enhanced dialog</p>
        </div>
        
        <h2>Color Boxes</h2>
        <div class="color-boxes">
            <div class="color-box red">Red #e74c3c</div>
            <div class="color-box blue">Blue #3498db</div>
            <div class="color-box green">Green #2ecc71</div>
            <div class="color-box yellow">Yellow #f1c40f</div>
            <div class="color-box purple">Purple #9b59b6</div>
            <div class="color-box orange">Orange #e67e22</div>
        </div>
        
        <div class="text-samples">
            <h2>Text Colors</h2>
            <p class="text-red">This is red text (#e74c3c)</p>
            <p class="text-blue">This is blue text (#3498db)</p>
            <p class="text-green">This is green text (#2ecc71)</p>
            <p class="gradient-text">This is gradient text</p>
        </div>
        
        <div class="border-sample">
            <h3>Border Sample</h3>
            <p>This box has a red border (#e74c3c). Try picking the border color!</p>
        </div>
        
        <div class="dark-section">
            <h3>Dark Section</h3>
            <p>This section has a dark background (#2c3e50) with white text (#ffffff).</p>
            <p>Try picking both the background and text colors!</p>
        </div>
    </div>
</body>
</html>
